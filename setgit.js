#!/usr/bin/env node
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// 生成随机小写字母
function generateRandomLetters(length) {
    const letters = 'abcdefghijklmnopqrstuvwxyz';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += letters.charAt(Math.floor(Math.random() * letters.length));
    }
    return result;
}

// 生成随机数字
function generateRandomNumbers(length) {
    let result = '';
    for (let i = 0; i < length; i++) {
        result += Math.floor(Math.random() * 10);
    }
    return result;
}

// 检查目录是否是Git仓库
function isGitRepo(dir) {
    try {
        const gitDir = path.join(dir, '.git');
        return fs.existsSync(gitDir) && fs.statSync(gitDir).isDirectory();
    } catch (e) {
        return false;
    }
}

// 设置Git配置
function setGitConfig(repoPath, username, email) {
    try {
        const originalDir = process.cwd();
        process.chdir(repoPath);
        
        execSync(`git config --local user.name "${username}"`);
        execSync(`git config --local user.email "${email}"`);
        
        process.chdir(originalDir);
        return true;
    } catch (error) {
        console.error(`在目录 ${repoPath} 设置Git配置失败:`, error.message);
        return false;
    }
}

function main() {
    // 解析命令行参数
    const args = process.argv.slice(2);
    let targetDir = process.cwd(); // 默认为当前目录
    
    if (args.length > 0) {
        if (args[0] === '--help' || args[0] === '-h') {
            console.log(`
用法: node set-git-config.js [目录路径]

选项:
  -h, --help    显示帮助信息
  [目录路径]    可选的目标Git仓库路径，默认为当前目录

示例:
  node set-git-config.js                  # 在当前目录设置
  node set-git-config.js ./my-project     # 在指定目录设置
            `);
            return;
        }
        targetDir = path.resolve(args[0]);
    }

    // 检查目标目录是否是Git仓库
    if (!isGitRepo(targetDir)) {
        console.error(`错误: ${targetDir} 不是一个Git仓库`);
        process.exit(1);
    }

    try {
        // 1. 生成随机6位字母和3位数字
        const randomLetters = generateRandomLetters(6);
        const randomNumbers = generateRandomNumbers(3);
        
        // 2. 构造随机前缀（jiwei_6字母3数字）
        const randomPrefix = `jiwei_${randomLetters}${randomNumbers}`;
        
        // 3. 生成随机6字母域名
        const randomDomainLetters = generateRandomLetters(6);
        const randomDomain = `${randomDomainLetters}.com`;
        
        // 4. 构造完整邮箱
        const randomEmail = `${randomPrefix}@${randomDomain}`;
        
        // 5. 设置Git本地配置
        if (setGitConfig(targetDir, randomPrefix, randomEmail)) {
            console.log('\n[Git配置已更新]');
            console.log(`仓库目录: ${targetDir}`);
            console.log(`用户名: ${randomPrefix}`);
            console.log(`邮箱  : ${randomEmail}\n`);
        }
        
    } catch (error) {
        console.error('执行出错:', error.message);
        process.exit(1);
    }
}

// 执行主函数
main();