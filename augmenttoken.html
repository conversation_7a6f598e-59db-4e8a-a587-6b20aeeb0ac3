

    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8">
      <title>Augment 授权</title>
      <style>
        body{font-family:sans-serif;padding:2em;line-height:1.6} a{color:#007bff} textarea,input{width:100%;box-sizing:border-box;padding:8px;margin-top:1em;margin-bottom:1em} button{margin-top:1em;padding:0.5em 1em;font-size:1em;cursor:pointer} .step{margin-bottom:2em} #loader{display:none}
      </style>
    </head>
    <body>
      <h1>Augment Auth Token 获取</h1>
      
      <div class="step">
        <h2>第一步：生成并打开授权链接</h2>
        <button id="authButton">点击生成并进行授权</button>
        <div id="loader">正在生成链接...</div>
        <p id="authLinkContainer" style="display:none;"><strong>授权链接:</strong> <a id="authLink" href="" target="_blank"></a></p>
      </div>

      <div class="step">
        <h2>第二步：粘贴授权响应并提交</h2>
        <p>授权成功后，将返回的 JSON 粘贴到下方，然后点击提交。</p>
        <form id="callbackForm">
          <textarea id="authResponse" placeholder='例如：{"code":"_000...","state":"...","tenant_url":"https://dxx.api.augmentcode.com/"}' required></textarea>
          <button type="submit">获取 Token</button>
        </form>
      </div>

      <script>
        // 将所有逻辑移至客户端
        const CLIENT_ID = "v";

        // Base64URL 编码函数 (浏览器原生不支持，需要手动实现)
        function base64urlEncode(buffer) {
          let base64 = btoa(String.fromCharCode.apply(null, new Uint8Array(buffer)));
          return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
        }

        function generateCodeVerifier() {
          const randomBytes = new Uint8Array(32);
          window.crypto.getRandomValues(randomBytes);
          return base64urlEncode(randomBytes);
        }

        async function generateCodeChallenge(verifier) {
          const encoder = new TextEncoder();
          const data = encoder.encode(verifier);
          const digest = await window.crypto.subtle.digest("SHA-256", data);
          return base64urlEncode(digest);
        }
        
        // 步骤 1: 点击按钮生成链接
        document.getElementById('authButton').addEventListener('click', async (e) => {
          e.target.disabled = true;
          document.getElementById('loader').style.display = 'block';

          const verifier = generateCodeVerifier();
          // 将 verifier 保存到 localStorage，以便在提交时使用
          localStorage.setItem('augment_code_verifier', verifier);
          
          const challenge = await generateCodeChallenge(verifier);
          const state = crypto.randomUUID();
          
          const params = new URLSearchParams({
            response_type: "code",
            code_challenge: challenge,
            client_id: CLIENT_ID,
            state: state,
            prompt: "login",
          });
          const authorizeUrl = `https://auth.augmentcode.com/authorize?${params.toString()}`;
          
          const link = document.getElementById('authLink');
          link.href = authorizeUrl;
          link.textContent = '点击这里在新窗口中授权';
          
          document.getElementById('loader').style.display = 'none';
          document.getElementById('authLinkContainer').style.display = 'block';
          
          // 自动在新窗口打开
          window.open(authorizeUrl, '_blank');
        });

        // 步骤 2: 提交表单
        document.getElementById('callbackForm').addEventListener('submit', async (e) => {
          e.preventDefault();
          
          const authResponseJson = document.getElementById('authResponse').value;
          const verifier = localStorage.getItem('augment_code_verifier');

          if (!verifier) {
            alert('错误：找不到 Code Verifier。请先点击第一步的按钮生成授权链接。');
            return;
          }

          try {
            const { code, tenant_url } = JSON.parse(authResponseJson);
            
            const formData = new FormData();
            formData.append('code', code);
            formData.append('tenant_url', tenant_url);
            formData.append('verifier', verifier);

            const response = await fetch('/callback', {
              method: 'POST',
              body: formData
            });

            if (!response.ok) {
              throw new Error('服务器处理失败: ' + await response.text());
            }

            // 成功后，将结果页的 HTML 渲染到当前页面
            const resultHtml = await response.text();
            document.body.innerHTML = resultHtml;

          } catch (error) {
            alert('提交失败: ' + error.message);
          }
        });
      </script>
    </body>
    </html>
  